<!-- Dashboard Layout -->
<div
  class="page-container content-spacing"
  *ngIf="tabService.tabsValue.length === 0"
>
  <!-- Welcome State -->
  <mat-card class="card-spacing">
    <mat-card-header>
      <div mat-card-avatar>
        <mat-icon>dashboard</mat-icon>
      </div>
      <mat-card-title>Welcome to NS Drive Dashboard</mat-card-title>
      <mat-card-subtitle>
        Start by creating your first sync operation
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <p>
        Sync operations allow you to synchronize files between local directories
        and cloud storage services. Each operation runs independently with its
        own configuration and profile.
      </p>

      <mat-list>
        <mat-list-item>
          <mat-icon matListItemIcon color="primary">sync</mat-icon>
          <div matListItemTitle>Real-time synchronization</div>
        </mat-list-item>
        <mat-list-item>
          <mat-icon matListItemIcon color="primary">cloud</mat-icon>
          <div matListItemTitle>Multiple cloud providers</div>
        </mat-list-item>
        <mat-list-item>
          <mat-icon matListItemIcon color="primary">settings</mat-icon>
          <div matListItemTitle>Customizable profiles</div>
        </mat-list-item>
      </mat-list>
    </mat-card-content>

    <mat-card-actions align="end">
      <button mat-raised-button color="primary" (click)="createTab()">
        <mat-icon>add</mat-icon>
        Create First Operation
      </button>
    </mat-card-actions>
  </mat-card>
</div>

<!-- ORIGINAL TEMPLATE COMMENTED OUT FOR DEBUG
<div
  class="page-container content-spacing"
  *ngIf="tabService.tabsValue.length === 0"
>
  <mat-card class="card-spacing">
    <mat-card-header>
      <div mat-card-avatar>
        <mat-icon>dashboard</mat-icon>
      </div>
      <mat-card-title>Welcome to NS Drive Dashboard</mat-card-title>
      <mat-card-subtitle>
        Start by creating your first sync operation
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <p>
        Sync operations allow you to synchronize files between local directories
        and cloud storage services. Each operation runs independently with its
        own configuration and profile.
      </p>

      <mat-list>
        <mat-list-item>
          <mat-icon matListItemIcon color="primary">sync</mat-icon>
          <div matListItemTitle>Real-time synchronization</div>
        </mat-list-item>
        <mat-list-item>
          <mat-icon matListItemIcon color="primary">cloud</mat-icon>
          <div matListItemTitle>Multiple cloud providers</div>
        </mat-list-item>
        <mat-list-item>
          <mat-icon matListItemIcon color="primary">settings</mat-icon>
          <div matListItemTitle>Customizable profiles</div>
        </mat-list-item>
      </mat-list>
    </mat-card-content>

    <mat-card-actions align="end">
      <button mat-raised-button color="primary" (click)="createTab()">
        <mat-icon>add</mat-icon>
        Create First Operation
      </button>
    </mat-card-actions>
  </mat-card>
</div>

<!-- Operations Dashboard - COMMENTED OUT FOR DEBUG
<div *ngIf="tabService.tabsValue.length > 0">
  <!-- Operations Header -->
<mat-toolbar>
  <mat-icon>sync</mat-icon>
  <span>Sync Operations</span>
  <span class="spacer"></span>
  <button
    mat-fab
    color="primary"
    (click)="createTab()"
    matTooltip="Add new operation"
  >
    <mat-icon>add</mat-icon>
  </button>
</mat-toolbar>

<!-- Tab Navigation -->
<mat-tab-group
  [selectedIndex]="getActiveTabIndex()"
  (selectedIndexChange)="onTabChange($event)"
>
  <mat-tab
    *ngFor="
      let tab of tabService.tabsValue;
      let i = index;
      trackBy: trackByTabId
    "
  >
    <ng-template mat-tab-label>
      <mat-icon>{{
        tab?.currentAction ? getActionIcon(tab.currentAction!) : "folder"
      }}</mat-icon>
      {{ tab?.name || "Operation " + (i + 1) }}
      <button
        mat-icon-button
        [matMenuTriggerFor]="tabMenu"
        (click)="$event.stopPropagation(); setCurrentTabForMenu(tab.id)"
        *ngIf="tab && tab.id"
      >
        <mat-icon>more_vert</mat-icon>
      </button>
    </ng-template>

    <!-- Tab Content -->
    <mat-card>
      <mat-card-header>
        <mat-card-title>
          <mat-icon>settings</mat-icon>
          Operation Controls
        </mat-card-title>
        <mat-card-subtitle>
          Configure and execute sync operations
        </mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <!-- Profile Selection -->
        <mat-form-field appearance="outline">
          <mat-label>Sync Profile</mat-label>
          <mat-select
            [value]="tab?.selectedProfileIndex"
            (selectionChange)="changeProfileTab($event.value, tab?.id)"
            [disabled]="!tab || !tab.id"
          >
            <mat-option [value]="null">
              <em>No profile selected</em>
            </mat-option>
            <mat-option
              *ngFor="
                let profile of appService.configInfo$.value.profiles;
                let idx = index
              "
              [value]="idx"
            >
              {{ profile.name }}
            </mat-option>
          </mat-select>
          <mat-icon matSuffix>folder_shared</mat-icon>
        </mat-form-field>

        <!-- Action Buttons -->
        <mat-card-actions *ngIf="validateTabProfileIndex(tab)">
          <button
            mat-raised-button
            [color]="tab.currentAction === Action.Pull ? 'warn' : 'primary'"
            [disabled]="
              (!validateTabProfileIndex(tab) &&
                tab.currentAction !== Action.Pull) ||
              tab.isStopping
            "
            (click)="
              tab.currentAction !== Action.Pull
                ? pullTab(tab.id)
                : stopCommandTab(tab.id)
            "
          >
            <mat-icon>{{
              tab.isStopping
                ? "hourglass_empty"
                : tab.currentAction === Action.Pull
                ? "stop"
                : "download"
            }}</mat-icon>
            {{
              tab.isStopping
                ? "Stopping..."
                : tab.currentAction === Action.Pull
                ? "Stop Pull"
                : "Pull"
            }}
          </button>

          <button
            mat-raised-button
            [color]="tab.currentAction === Action.Push ? 'warn' : 'accent'"
            [disabled]="
              !validateTabProfileIndex(tab) && tab.currentAction !== Action.Push
            "
            (click)="
              tab.currentAction !== Action.Push
                ? pushTab(tab.id)
                : stopCommandTab(tab.id)
            "
          >
            <mat-icon>{{
              tab.currentAction === Action.Push ? "stop" : "upload"
            }}</mat-icon>
            {{ tab.currentAction === Action.Push ? "Stop Push" : "Push" }}
          </button>

          <button
            mat-raised-button
            [color]="tab.currentAction === Action.Bi ? 'warn' : 'primary'"
            [disabled]="
              !validateTabProfileIndex(tab) && tab.currentAction !== Action.Bi
            "
            (click)="
              tab.currentAction !== Action.Bi
                ? biTab(tab.id)
                : stopCommandTab(tab.id)
            "
          >
            <mat-icon>{{
              tab.currentAction === Action.Bi ? "stop" : "sync"
            }}</mat-icon>
            {{ tab.currentAction === Action.Bi ? "Stop Sync" : "Sync" }}
          </button>

          <button
            mat-raised-button
            color="warn"
            [disabled]="
              !validateTabProfileIndex(tab) &&
              tab.currentAction !== Action.BiResync
            "
            (click)="
              tab.currentAction !== Action.BiResync
                ? biResyncTab(tab.id)
                : stopCommandTab(tab.id)
            "
          >
            <mat-icon>{{
              tab.currentAction === Action.BiResync ? "stop" : "refresh"
            }}</mat-icon>
            {{
              tab.currentAction === Action.BiResync ? "Stop Resync" : "Resync"
            }}
          </button>
        </mat-card-actions>

        <!-- Status Display -->
        <div *ngIf="tab.currentAction">
          <mat-progress-bar mode="indeterminate"></mat-progress-bar>
          <mat-chip-set>
            <mat-chip>
              <mat-icon matChipAvatar>{{
                getActionIcon(tab.currentAction)
              }}</mat-icon>
              {{ getActionLabel(tab.currentAction) }}
            </mat-chip>
          </mat-chip-set>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Info Cards -->
    <mat-card>
      <mat-card-header>
        <mat-card-title>
          <mat-icon>folder</mat-icon>
          Working Directory
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <code>{{ (appService.configInfo$ | async)?.working_dir }}</code>
      </mat-card-content>
    </mat-card>

    <!-- Console Output -->
    <mat-card>
      <mat-card-header>
        <mat-card-title>
          <mat-icon>terminal</mat-icon>
          Console Output
        </mat-card-title>
        <span class="spacer"></span>
        <button
          mat-icon-button
          (click)="clearTabOutput(tab.id)"
          matTooltip="Clear output"
        >
          <mat-icon>clear</mat-icon>
        </button>
      </mat-card-header>
      <mat-card-content>
        <pre>{{ tab.data.join("\n") || "No output yet..." }}</pre>
      </mat-card-content>
    </mat-card>
  </mat-tab>
</mat-tab-group>

<!-- Shared Tab Menu -->
<mat-menu #tabMenu="matMenu">
  <button mat-menu-item (click)="startRenameTab(currentTabIdForMenu)">
    <mat-icon>edit</mat-icon>
    <span>Rename</span>
  </button>
  <button mat-menu-item (click)="deleteTab(currentTabIdForMenu)">
    <mat-icon>delete</mat-icon>
    <span>Delete</span>
  </button>
</mat-menu>
END DEBUG COMMENT -->

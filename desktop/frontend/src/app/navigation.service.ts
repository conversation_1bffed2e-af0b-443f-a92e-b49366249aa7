import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export type NavigationState = 
  | { page: 'profiles' }
  | { page: 'profile-edit'; profileIndex: number }
  | { page: 'remotes' }
  | { page: 'home' };

@Injectable({
  providedIn: 'root'
})
export class NavigationService {
  private navigationState$ = new BehaviorSubject<NavigationState>({ page: 'profiles' });

  get currentState$() {
    return this.navigationState$.asObservable();
  }

  get currentState() {
    return this.navigationState$.value;
  }

  navigateToProfiles() {
    this.navigationState$.next({ page: 'profiles' });
  }

  navigateToProfileEdit(profileIndex: number) {
    this.navigationState$.next({ page: 'profile-edit', profileIndex });
  }

  navigateToRemotes() {
    this.navigationState$.next({ page: 'remotes' });
  }

  navigateToHome() {
    this.navigationState$.next({ page: 'home' });
  }
}
